#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 G代码处理程序 (最终修正版 v7)

核心修正:
1. 修复上电逻辑: 在 `robot_power_on_if_needed` 中添加了 set_servo_state(..., 1) 的关键步骤。
2. 保持正确流程: 连接 -> 获取位置 -> 上电 -> 切换模式 -> 执行。
"""

import sys
import os
import time
import re
import math
from typing import List, Tuple, Optional

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

# === 全局配置参数 ===
GCODE_FILE = "jiyi.Gcode"
USER_COORD_NUMBER = 1
G0_VELOCITY_PERCENT = 50
G1_VELOCITY_PERCENT = 10
ACCEL_PERCENT = 20
SMOOTHING_LEVEL = 0
G0_FIXED_A, G0_FIXED_B, G0_FIXED_C = 0.0, 0.0, 0.0
G1_DEFAULT_A, G1_DEFAULT_B, G1_DEFAULT_C = 0.0, 0.0, 0.0
GCODE_TO_ROBOT_OFFSET_A, GCODE_TO_ROBOT_OFFSET_B, GCODE_TO_ROBOT_OFFSET_C = 180.0, 0.0, 0.0
ANGLE_CHANGE_THRESHOLD = 20.0
SMOOTH_INTERPOLATION_STEPS = 5
QUEUE_BATCH_SIZE = 20
TIMEOUT_SECONDS = 60

class GCodeInstruction:
    def __init__(self, instruction_type: str, x: float, y: float, z: float,
                 a: Optional[float] = None, b: Optional[float] = None, c: Optional[float] = None):
        self.type, self.x, self.y, self.z, self.a, self.b, self.c = instruction_type, x, y, z, a, b, c
    def get_position(self) -> Tuple[float, float, float]: return (self.x, self.y, self.z)
    def __repr__(self): return f"{self.type}(X={self.x:.3f}, Y={self.y:.3f}, Z={self.z:.3f}, A={self.a}, B={self.b}, C={self.c})"

class AngleSmoothing:
    @staticmethod
    def normalize_angle_degrees(angle: float) -> float:
        while angle > 180: angle -= 360
        while angle <= -180: angle += 360
        return angle
    @staticmethod
    def calculate_angle_difference(a1: float, a2: float) -> float: return AngleSmoothing.normalize_angle_degrees(a2 - a1)
    @staticmethod
    def detect_angle_jump(p1: Tuple, p2: Tuple, t: float = ANGLE_CHANGE_THRESHOLD) -> bool:
        return any(abs(AngleSmoothing.calculate_angle_difference(c1, c2)) > t for c1, c2 in zip(p1, p2))
    @staticmethod
    def generate_smooth_transition(s: Tuple, e: Tuple, steps: int = SMOOTH_INTERPOLATION_STEPS) -> List[Tuple]:
        if steps <= 1: return [e]
        return [tuple(AngleSmoothing.normalize_angle_degrees(s_a + AngleSmoothing.calculate_angle_difference(s_a, e_a) * (i / steps))
                      for s_a, e_a in zip(s, e)) for i in range(1, steps + 1)]

class ImprovedGCodeProcessor:
    def __init__(self):
        self.socket_fd = -1
        self.last_processed_angles = None

    def parse_gcode_file(self, filepath: str) -> List[GCodeInstruction]:
        print(f"📄 正在解析G代码文件: {filepath}")
        instructions = []
        coord_regex = re.compile(r'([XYZABC])([-+]?\d*\.?\d+)')
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                for ln, line in enumerate(f, 1):
                    line = line.strip().upper()
                    if line.startswith(('G0', 'G1')):
                        coords = dict(coord_regex.findall(line))
                        if all(k in coords for k in 'XYZ'):
                            instructions.append(GCodeInstruction(line[:2], float(coords['X']), float(coords['Y']), float(coords['Z']),
                                                                 float(coords['A']) if 'A' in coords else None,
                                                                 float(coords['B']) if 'B' in coords else None,
                                                                 float(coords['C']) if 'C' in coords else None))
            print(f"✅ 解析完成: 共 {len(instructions)} 个有效指令")
            return instructions
        except Exception as e:
            print(f"❌ 解析G代码文件时发生错误: {e}")
            return []

    def get_instruction_angles(self, instr: GCodeInstruction) -> Tuple[float, float, float]:
        if instr.type == 'G0': return G0_FIXED_A, G0_FIXED_B, G0_FIXED_C
        return (instr.a if instr.a is not None else G1_DEFAULT_A, instr.b if instr.b is not None else G1_DEFAULT_B, instr.c if instr.c is not None else G1_DEFAULT_C)

    def convert_to_robot_angles(self, angles: Tuple) -> Tuple:
        return (AngleSmoothing.normalize_angle_degrees(angles[0] + GCODE_TO_ROBOT_OFFSET_A),
                AngleSmoothing.normalize_angle_degrees(angles[1] + GCODE_TO_ROBOT_OFFSET_B),
                AngleSmoothing.normalize_angle_degrees(angles[2] + GCODE_TO_ROBOT_OFFSET_C))

    def create_move_command(self, pos: Tuple, angles: Tuple, vel: int) -> nrc.MoveCmd:
        cmd = nrc.MoveCmd()
        cmd.targetPosType, cmd.targetPosValue = nrc.PosType_data, nrc.VectorDouble()
        for v in pos: cmd.targetPosValue.append(v)
        for a in angles: cmd.targetPosValue.append(math.radians(a))
        cmd.coord, cmd.userNum, cmd.velocity, cmd.acc, cmd.dec, cmd.pl = 3, USER_COORD_NUMBER, vel, ACCEL_PERCENT, ACCEL_PERCENT, SMOOTHING_LEVEL
        return cmd
    
    def prepare_initial_transition(self, first_instruction: GCodeInstruction) -> List[nrc.MoveCmd]:
        print("\n🛡️ 正在准备初始过渡动作...")
        print("   正在获取机器人当前位置...")
        current_pos_vector = nrc.VectorDouble()
        result_code = nrc.get_current_position(self.socket_fd, 3, current_pos_vector)
        
        if result_code != 0:
            print(f"❌ 获取机器人当前位置失败！错误码: {result_code}。")
            print(f"   请确保机器人在示教模式下，并且用户坐标系({USER_COORD_NUMBER})已定义。")
            return []
            
        current_position = tuple(current_pos_vector[i] for i in range(3))
        current_angles_rad = tuple(current_pos_vector[i] for i in range(3, 6))
        current_angles_deg = tuple(math.degrees(a) for a in current_angles_rad)
        
        print(f"   当前位置: X={current_position[0]:.2f} Y={current_position[1]:.2f} Z={current_position[2]:.2f}")
        print(f"   当前姿态: A={current_angles_deg[0]:.2f} B={current_angles_deg[1]:.2f} C={current_angles_deg[2]:.2f}")

        target_gcode_angles = self.get_instruction_angles(first_instruction)
        target_robot_angles = self.convert_to_robot_angles(target_gcode_angles)
        print(f"   首点目标姿态: A={target_robot_angles[0]:.2f} B={target_robot_angles[1]:.2f} C={target_robot_angles[2]:.2f}")

        rotate_cmd = self.create_move_command(current_position, target_robot_angles, G1_VELOCITY_PERCENT)
        self.last_processed_angles = target_robot_angles
        print("   步骤1: 原地调整姿态指令已生成。")
        return [rotate_cmd]
    
    def prepare_gcode_commands(self, instructions: List[GCodeInstruction]) -> List[nrc.MoveCmd]:
        print("\n🛠️ 正在准备G代码运动指令，并应用角度平滑...")
        processed_commands = []
        for i, instruction in enumerate(instructions):
            velocity = G0_VELOCITY_PERCENT if instruction.type == 'G0' else G1_VELOCITY_PERCENT
            robot_angles = self.convert_to_robot_angles(self.get_instruction_angles(instruction))
            if self.last_processed_angles and AngleSmoothing.detect_angle_jump(self.last_processed_angles, robot_angles):
                print(f"  🔄 检测到角度突变 (指令 {i+1})，插入平滑过渡...")
                last_pos = instructions[i-1].get_position()
                transitions = AngleSmoothing.generate_smooth_transition(self.last_processed_angles, robot_angles)
                for t_angle in transitions[:-1]:
                    processed_commands.append(self.create_move_command(last_pos, t_angle, velocity))
                self.last_processed_angles = transitions[-2] if len(transitions) > 1 else self.last_processed_angles

            processed_commands.append(self.create_move_command(instruction.get_position(), robot_angles, velocity))
            self.last_processed_angles = robot_angles
        print(f"✅ G代码指令准备完成，共生成 {len(processed_commands)} 个运动命令。")
        return processed_commands

    def _execute_batches(self, commands: List[nrc.MoveCmd], name: str) -> bool:
        if not commands: return True
        print(f"\n🚀 开始执行 {name} 指令，共 {len(commands)} 个命令")
        total, batch_count = len(commands), (len(commands) + QUEUE_BATCH_SIZE - 1) // QUEUE_BATCH_SIZE
        for i in range(batch_count):
            start, end = i * QUEUE_BATCH_SIZE, min((i + 1) * QUEUE_BATCH_SIZE, total)
            batch = commands[start:end]
            print(f"\n--- {name} 批次 {i + 1}/{batch_count} (指令 {start + 1}-{end}) ---")
            if nrc.queue_motion_clear_Data(self.socket_fd) != 0: return False
            for cmd in batch:
                if nrc.queue_motion_push_back_moveL(self.socket_fd, cmd) != 0: return False
            if not self.send_queue_and_wait(len(batch), f"{name} 批次 {i + 1}"): return False
            if i < batch_count - 1: time.sleep(0.5)
        return True
    
    def send_queue_and_wait(self, size: int, name: str) -> bool:
        if size == 0: return True
        print(f"📤 正在发送 {name} (大小: {size})", end="...")
        if nrc.queue_motion_send_to_controller(self.socket_fd, size) != 0: print(" ❌ 发送失败!"); return False
        print(" 等待执行...", end="", flush=True)
        start_t = time.time()
        while time.time() - start_t < TIMEOUT_SECONDS + (2 * size):
            running_status_res = nrc.get_robot_running_state(self.socket_fd, 0)
            queue_len_res = nrc.queue_motion_get_queuelen(self.socket_fd, 0)
            if running_status_res[1] == 0 and queue_len_res[1] == 0:
                print(" ✅"); return True
            time.sleep(0.1)
        print(" ❌ 超时!"); return False

    def setup_queue_mode(self) -> bool:
        print("🔧 正在设置队列模式...")
        try:
            nrc.set_current_mode(self.socket_fd, 2); time.sleep(0.1)
            if nrc.set_speed(self.socket_fd, 100) != 0: return False
            if nrc.queue_motion_set_status(self.socket_fd, True) != 0: return False
            print("✅ 队列模式及全局速度设置成功。")
            return True
        except Exception as e: print(f"❌ 设置队列模式时发生错误: {e}"); return False

    def cleanup_queue_mode(self):
        print("🧹 正在清理队列模式..."); nrc.queue_motion_set_status(self.socket_fd, False); nrc.set_current_mode(self.socket_fd, 0); print("✅ 已切换回示教模式。")

def robot_power_on_if_needed(sock: int) -> bool:
    servo_state_res = nrc.get_servo_state(sock, 0)
    if servo_state_res[1] == 3: # 状态3代表已上电
        print("✅ 机器人伺服已上电。")
        return True
        
    print("ℹ️ 机器人需要上电，开始标准上电流程...")
    
    nrc.clear_error(sock)
    time.sleep(0.2)
    
    print("   设置伺服为就绪状态...")
    if nrc.set_servo_state(sock, 1) != 0: # 状态1代表就绪
        print("❌ 设置伺服就绪失败！")
        return False
    time.sleep(0.5)

    print("   执行上电...")
    if nrc.set_servo_poweron(sock) != 0:
        print("❌ 上电失败！请检查机器人物理状态（如急停按钮）。")
        return False
    time.sleep(1.5)
    
    final_state_res = nrc.get_servo_state(sock, 0)
    if final_state_res[1] == 3:
        print("✅ 机器人上电成功！")
        return True
    else:
        print(f"❌ 上电后状态异常: {final_state_res[1]}")
        return False

def robot_power_off(sock: int): print("\nℹ️ 正在安全下电..."); nrc.set_servo_poweroff(sock); time.sleep(1); print("✅ 机器人已下电。")

def execute_improved_gcode():
    print("=" * 80 + "\nINEXBOT机械臂 G代码处理程序 (最终修正版 v7)\n" + "=" * 80)
    processor = ImprovedGCodeProcessor()
    try:
        all_instructions = processor.parse_gcode_file(GCODE_FILE)
        if not all_instructions: return

        print(f"\n🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}..."); 
        processor.socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if processor.socket_fd <= 0: return
        print(f"✅ 连接成功！Socket ID: {processor.socket_fd}")
        time.sleep(0.2)
        
        # 严格按照“先获取位置，再上电和设置模式”的特定流程
        # 1. 在示教模式下，获取初始位置
        transition_commands = processor.prepare_initial_transition(all_instructions[0])
        if not transition_commands: return

        # 2. 上电
        if not robot_power_on_if_needed(processor.socket_fd): return
        
        # 3. 进行模式设置
        nrc.set_user_coord_number(processor.socket_fd, USER_COORD_NUMBER)
        if not processor.setup_queue_mode(): return
        
        # 4. 执行初始过渡
        if not processor._execute_batches(transition_commands, "初始过渡"):
            print("❌ 初始过渡失败，程序终止。"); return

        # 5. 执行G代码
        gcode_commands = processor.prepare_gcode_commands(all_instructions)
        if not processor._execute_batches(gcode_commands, "G-Code"):
            print("❌ G代码执行失败"); return
            
        print("\n" + "=" * 80 + "\n🎉 G代码处理全部完成！\n" + "=" * 80)
    except Exception as e:
        print(f"\n❌ 主程序发生严重错误: {e}"); import traceback; traceback.print_exc()
    finally:
        if processor.socket_fd > 0:
            processor.cleanup_queue_mode(); robot_power_off(processor.socket_fd)
            print("🔌 正在断开连接..."); nrc.disconnect_robot(processor.socket_fd); print("✅ 连接已断开。")

if __name__ == "__main__":
    execute_improved_gcode()